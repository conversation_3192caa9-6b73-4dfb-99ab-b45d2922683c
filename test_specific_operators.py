#!/usr/bin/env python3
"""
Test specific comparison operators to debug the validation issues.
"""

import requests
import json

API_BASE_URL = "http://localhost:8000"

def test_in_operator():
    """Test the IN operator specifically"""
    print("Testing IN operator...")
    
    # Create test memory first
    memory_data = {
        "messages": [{"role": "user", "content": "<PERSON> works with travel-agent."}],
        "user_id": "alice",
        "agent_id": "travel-agent",
        "metadata": {"category": "work"}
    }
    
    create_response = requests.post(f"{API_BASE_URL}/v1/memories/", json=memory_data)
    print(f"Memory creation: {create_response.status_code}")
    
    # Test IN operator
    search_data = {
        "query": "work",
        "user_id": "alice",
        "filters": {
            "agent_id": {"in": ["travel-agent", "sports-agent"]}
        }
    }
    
    response = requests.post(f"{API_BASE_URL}/v2/memories/search/", json=search_data)
    print(f"IN operator test: {response.status_code}")
    if response.status_code != 200:
        print(f"Error: {response.text}")
    else:
        data = response.json()
        print(f"Results: {len(data.get('results', []))}")

def test_gt_operator():
    """Test the GT operator specifically"""
    print("\nTesting GT operator...")
    
    # Create test memory with priority
    memory_data = {
        "messages": [{"role": "user", "content": "Alice has high priority task."}],
        "user_id": "alice",
        "metadata": {"priority": 8}
    }
    
    create_response = requests.post(f"{API_BASE_URL}/v1/memories/", json=memory_data)
    print(f"Memory creation: {create_response.status_code}")
    
    # Test GT operator
    search_data = {
        "query": "task",
        "user_id": "alice",
        "filters": {
            "priority": {"gt": 5}
        }
    }
    
    response = requests.post(f"{API_BASE_URL}/v2/memories/search/", json=search_data)
    print(f"GT operator test: {response.status_code}")
    if response.status_code != 200:
        print(f"Error: {response.text}")
    else:
        data = response.json()
        print(f"Results: {len(data.get('results', []))}")

def test_filter_with_identifier():
    """Test filter that includes identifier"""
    print("\nTesting filter with identifier...")
    
    search_data = {
        "query": "test",
        "filters": {
            "AND": [
                {"user_id": "alice"},
                {"category": "work"}
            ]
        }
    }
    
    response = requests.post(f"{API_BASE_URL}/v2/memories/search/", json=search_data)
    print(f"Filter with identifier test: {response.status_code}")
    if response.status_code != 200:
        print(f"Error: {response.text}")
    else:
        data = response.json()
        print(f"Results: {len(data.get('results', []))}")

def cleanup():
    """Clean up test data"""
    print("\nCleaning up...")
    response = requests.delete(f"{API_BASE_URL}/v1/memories/", params={"user_id": "alice"})
    print(f"Cleanup: {response.status_code}")

if __name__ == "__main__":
    test_in_operator()
    test_gt_operator()
    test_filter_with_identifier()
    cleanup()
