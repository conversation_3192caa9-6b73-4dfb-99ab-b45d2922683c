#!/usr/bin/env python3
"""
V2 Search API Filter Processing Improvements

This file contains suggested improvements for the process_v2_filters function
to better support the comparison operators described in the documentation.
"""

from typing import Dict, Any, List, Union


def improved_process_v2_filters(filters: Dict[str, Any]) -> Dict[str, Any]:
    """
    Improved V2 API complex filters processing with better support for
    comparison operators and logical operations.
    
    Supports:
    - AND/OR/NOT logical operators (improved)
    - All comparison operators: in, gte, lte, gt, lt, ne, icontains
    - Wildcard support
    - Better error handling
    """
    if not filters:
        return {}

    processed_filters = {}
    simple_filter_keys = {"user_id", "agent_id", "run_id"}
    
    # Handle simple filters (backward compatibility)
    for key in simple_filter_keys:
        if key in filters:
            processed_filters[key] = filters[key]

    # Process complex filters
    complex_filters = _process_complex_filters(filters, simple_filter_keys)
    
    if complex_filters:
        processed_filters["filters"] = complex_filters

    return processed_filters


def _process_complex_filters(filters: Dict[str, Any], simple_keys: set) -> Dict[str, Any]:
    """Process complex filters including logical and comparison operators."""
    complex_filters = {}
    
    # Handle logical operators
    if "AND" in filters:
        and_result = _process_and_operator(filters["AND"], simple_keys)
        complex_filters.update(and_result)
    
    if "OR" in filters:
        or_result = _process_or_operator(filters["OR"], simple_keys)
        complex_filters.update(or_result)
    
    if "NOT" in filters:
        not_result = _process_not_operator(filters["NOT"], simple_keys)
        complex_filters.update(not_result)
    
    # Handle direct field filters
    for key, value in filters.items():
        if key in {"AND", "OR", "NOT"} or key in simple_keys:
            continue
        
        processed_value = _process_field_filter(key, value)
        complex_filters.update(processed_value)
    
    return complex_filters


def _process_and_operator(and_conditions: List[Dict], simple_keys: set) -> Dict[str, Any]:
    """Process AND logical operator."""
    result = {}
    
    if isinstance(and_conditions, list):
        for condition in and_conditions:
            if isinstance(condition, dict):
                sub_result = improved_process_v2_filters(condition)
                
                # Merge complex filters
                if "filters" in sub_result:
                    result.update(sub_result["filters"])
                
                # Handle simple keys (they should be consistent in AND)
                for key in simple_keys:
                    if key in sub_result:
                        if key in result and result[key] != sub_result[key]:
                            # Conflicting values in AND - this should be handled
                            pass
                        else:
                            result[key] = sub_result[key]
    
    return result


def _process_or_operator(or_conditions: List[Dict], simple_keys: set) -> Dict[str, Any]:
    """
    Process OR logical operator.
    
    Note: True OR logic requires post-processing since most vector stores
    don't support OR at the filter level. This implementation provides
    a foundation for application-level OR processing.
    """
    result = {}
    
    if isinstance(or_conditions, list) and or_conditions:
        # For now, we'll mark this as an OR condition that needs special handling
        # In a full implementation, this would require multiple queries and merging
        result["__or_conditions"] = []
        
        for condition in or_conditions:
            if isinstance(condition, dict):
                sub_result = improved_process_v2_filters(condition)
                result["__or_conditions"].append(sub_result)
        
        # As a fallback, use the first condition (current behavior)
        if result["__or_conditions"]:
            first_condition = result["__or_conditions"][0]
            if "filters" in first_condition:
                result.update(first_condition["filters"])
    
    return result


def _process_not_operator(not_conditions: Dict, simple_keys: set) -> Dict[str, Any]:
    """
    Process NOT logical operator.
    
    Note: NOT logic requires post-processing since most vector stores
    don't support NOT at the filter level.
    """
    result = {}
    
    if isinstance(not_conditions, dict):
        # Mark this as a NOT condition for post-processing
        sub_result = improved_process_v2_filters(not_conditions)
        result["__not_condition"] = sub_result
    
    return result


def _process_field_filter(field_name: str, field_value: Any) -> Dict[str, Any]:
    """Process individual field filters with comparison operators."""
    result = {}
    
    if isinstance(field_value, dict):
        # Handle comparison operators
        for operator, value in field_value.items():
            if operator == "in":
                # Handle 'in' operator - value should be a list
                if isinstance(value, list):
                    result[f"{field_name}__in"] = value
                else:
                    # Convert single value to list for compatibility
                    result[f"{field_name}__in"] = [value]
            
            elif operator == "gte":
                result[f"{field_name}__gte"] = value
            
            elif operator == "lte":
                result[f"{field_name}__lte"] = value
            
            elif operator == "gt":
                result[f"{field_name}__gt"] = value
            
            elif operator == "lt":
                result[f"{field_name}__lt"] = value
            
            elif operator == "ne":
                result[f"{field_name}__ne"] = value
            
            elif operator == "icontains":
                result[f"{field_name}__icontains"] = value
            
            else:
                # Unknown operator, treat as direct assignment
                result[field_name] = field_value
    
    elif field_value == "*":
        # Handle wildcard - match any value
        result[f"{field_name}__exists"] = True
    
    else:
        # Direct value assignment
        result[field_name] = field_value
    
    return result


def validate_v2_search_request(request_data: Dict[str, Any]) -> List[str]:
    """
    Validate V2 search request and return list of validation errors.
    """
    errors = []
    
    # Check required query
    if not request_data.get("query"):
        errors.append("Query is required")
    
    # Check required identifiers
    identifiers = ["user_id", "agent_id", "run_id"]
    has_identifier = any(request_data.get(id_field) for id_field in identifiers)
    
    if not has_identifier:
        # Check if identifiers are in filters
        filters = request_data.get("filters", {})
        has_filter_identifier = any(
            _has_identifier_in_filters(filters, id_field) 
            for id_field in identifiers
        )
        
        if not has_filter_identifier:
            errors.append("At least one identifier (user_id, agent_id, run_id) is required")
    
    return errors


def _has_identifier_in_filters(filters: Dict[str, Any], identifier: str) -> bool:
    """Check if an identifier exists anywhere in the filter structure."""
    if identifier in filters:
        return True
    
    # Check in logical operators
    for logical_op in ["AND", "OR", "NOT"]:
        if logical_op in filters:
            conditions = filters[logical_op]
            if logical_op == "NOT":
                conditions = [conditions] if isinstance(conditions, dict) else []
            
            if isinstance(conditions, list):
                for condition in conditions:
                    if isinstance(condition, dict) and _has_identifier_in_filters(condition, identifier):
                        return True
    
    return False


# Example usage and test cases
def test_improved_filters():
    """Test cases for the improved filter processing."""
    
    # Test case 1: OR with IN operator
    test_filter_1 = {
        "OR": [
            {"user_id": "alice"},
            {"agent_id": {"in": ["travel-agent", "sports-agent"]}}
        ]
    }
    
    result_1 = improved_process_v2_filters(test_filter_1)
    print("Test 1 - OR with IN:", result_1)
    
    # Test case 2: AND with comparison operators
    test_filter_2 = {
        "AND": [
            {"user_id": "alice"},
            {"priority": {"gte": 5, "lte": 8}}
        ]
    }
    
    result_2 = improved_process_v2_filters(test_filter_2)
    print("Test 2 - AND with comparison:", result_2)
    
    # Test case 3: Complex nested filters
    test_filter_3 = {
        "AND": [
            {"user_id": "alice"},
            {
                "OR": [
                    {"category": "hobbies"},
                    {"priority": {"gt": 8}}
                ]
            }
        ]
    }
    
    result_3 = improved_process_v2_filters(test_filter_3)
    print("Test 3 - Complex nested:", result_3)
    
    # Test case 4: Wildcard
    test_filter_4 = {
        "AND": [
            {"user_id": "alice"},
            {"run_id": "*"}
        ]
    }
    
    result_4 = improved_process_v2_filters(test_filter_4)
    print("Test 4 - Wildcard:", result_4)


if __name__ == "__main__":
    test_improved_filters()
