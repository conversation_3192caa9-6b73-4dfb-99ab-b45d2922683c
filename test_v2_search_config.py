#!/usr/bin/env python3
"""
Configuration file for V2 Search API testing
"""

import os
from typing import Optional

class TestConfig:
    """Configuration for V2 Search API tests"""
    
    def __init__(self):
        self.api_key = os.getenv("MEM0_API_KEY")
        self.org_id = os.getenv("MEM0_ORG_ID")
        self.project_id = os.getenv("MEM0_PROJECT_ID")
        
        # Test user IDs to avoid conflicts
        self.test_users = {
            "basic": "alice_v2_test",
            "filters": "filter_v2_test", 
            "advanced": "advanced_v2_test",
            "criteria": "criteria_v2_test",
            "graph": "graph_v2_test",
            "timestamp": "timestamp_v2_test",
            "async": "async_v2_test"
        }
        
        # Test data for different scenarios
        self.test_messages = {
            "basic": [
                {"role": "user", "content": "My name is <PERSON> and I love playing tennis"},
                {"role": "assistant", "content": "Hi Alice! Tennis is a great sport."},
                {"role": "user", "content": "I also enjoy cooking Italian food"},
                {"role": "user", "content": "I'm allergic to peanuts and shellfish"},
            ],
            "sports": [
                {"role": "user", "content": "I love playing basketball"},
                {"role": "assistant", "content": "Basketball is fun!"}
            ],
            "cooking": [
                {"role": "user", "content": "I enjoy cooking Thai food"},
                {"role": "assistant", "content": "Thai food is delicious!"}
            ],
            "emotions": [
                {"role": "user", "content": "What a beautiful sunny day! I feel so refreshed and ready to take on anything!"},
                {"role": "user", "content": "I've always wondered how storms form—what triggers them in the atmosphere?"},
                {"role": "user", "content": "It's been raining for days, and it just makes everything feel heavier."},
                {"role": "user", "content": "Finally I get time to draw something today, after a long time!! I am super happy today."}
            ],
            "personal": [
                {"role": "user", "content": "My name is Joseph"},
                {"role": "assistant", "content": "Hello Joseph, it's nice to meet you!"},
                {"role": "user", "content": "I'm from Seattle and I work as a software engineer"}
            ]
        }
        
        # Custom categories for testing
        self.custom_categories = [
            {"sports_preferences": "User's favorite sports and athletic activities"},
            {"food_preferences": "User's dietary preferences and favorite cuisines"},
            {"personal_info": "Basic personal information about the user"}
        ]
        
        # Retrieval criteria for testing
        self.retrieval_criteria = [
            {
                "name": "joy",
                "description": "Measure the intensity of positive emotions such as happiness, excitement, or amusement expressed in the sentence. A higher score reflects greater joy.",
                "weight": 3
            },
            {
                "name": "curiosity", 
                "description": "Assess the extent to which the sentence reflects inquisitiveness, interest in exploring new information, or asking questions. A higher score reflects stronger curiosity.",
                "weight": 2
            },
            {
                "name": "sadness",
                "description": "Evaluate the presence and depth of sadness or negative emotional tone, including expressions of disappointment, frustration, or sorrow. A higher score reflects greater sadness.",
                "weight": 1
            }
        ]
        
        # Custom instructions for testing
        self.custom_instructions = """
        Your Task: Extract ONLY health-related information from conversations, focusing on the following areas:

        1. Medical Conditions, Symptoms, and Diagnoses:
           - Illnesses, disorders, or symptoms (e.g., fever, diabetes).
           - Confirmed or suspected diagnoses.

        2. Medications, Treatments, and Procedures:
           - Prescription or OTC medications (names, dosages).
           - Treatments, therapies, or medical procedures.

        3. Diet, Exercise, and Sleep:
           - Dietary habits, fitness routines, and sleep patterns.

        4. Doctor Visits and Appointments:
           - Past, upcoming, or regular medical visits.

        5. Health Metrics:
           - Data like weight, BP, cholesterol, or sugar levels.

        Guidelines:
        - Focus solely on health-related content.
        - Maintain clarity and context accuracy while recording.
        """
    
    def validate_config(self) -> bool:
        """Validate that required configuration is present"""
        if not self.api_key:
            print("Warning: MEM0_API_KEY not found in environment variables")
            return False
        
        print("✅ Configuration validated successfully")
        print(f"API Key: {'*' * 10 + self.api_key[-4:] if self.api_key else 'Not set'}")
        print(f"Org ID: {self.org_id or 'Not set'}")
        print(f"Project ID: {self.project_id or 'Not set'}")
        
        return True
    
    def get_test_user(self, test_type: str) -> str:
        """Get test user ID for a specific test type"""
        return self.test_users.get(test_type, f"test_user_{test_type}")
    
    def get_test_messages(self, test_type: str) -> list:
        """Get test messages for a specific test type"""
        return self.test_messages.get(test_type, [])
    
    def print_test_info(self):
        """Print test configuration information"""
        print("\n" + "=" * 50)
        print("V2 SEARCH API TEST CONFIGURATION")
        print("=" * 50)
        print(f"API Key: {'Set' if self.api_key else 'Not set'}")
        print(f"Org ID: {self.org_id or 'Not set'}")
        print(f"Project ID: {self.project_id or 'Not set'}")
        print(f"Test Users: {len(self.test_users)} configured")
        print(f"Test Message Types: {len(self.test_messages)} configured")
        print("=" * 50)

# Global config instance
config = TestConfig() 