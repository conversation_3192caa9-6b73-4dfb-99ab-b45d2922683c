[{"test": "Basic V2 Search", "success": true, "details": "Found 2 results", "timestamp": "2025-08-04T09:45:59.229092"}, {"test": "OR Logical Operator", "success": false, "details": "HTTP 400: {\"detail\":\"At least one identifier (user_id, agent_id, run_id) is required.\"}", "timestamp": "2025-08-04T09:45:59.232132"}, {"test": "AND Logical Operator", "success": false, "details": "HTTP 400: {\"detail\":\"At least one identifier (user_id, agent_id, run_id) is required.\"}", "timestamp": "2025-08-04T09:45:59.235016"}, {"test": "IN Comparison Operator", "success": false, "details": "HTTP 500: {\"detail\":\"3 validation errors for MatchValue\\nvalue.bool\\n  Input should be a valid boolean [type=bool_type, input_value={'in': ['travel-agent', 'sports-agent']}, input_type=dict]\\n    For further information visit https://errors.pydantic.dev/2.10/v/bool_type\\nvalue.int\\n  Input should be a valid integer [type=int_type, input_value={'in': ['travel-agent', 'sports-agent']}, input_type=dict]\\n    For further information visit https://errors.pydantic.dev/2.10/v/int_type\\nvalue.str\\n  Input should be a valid string [type=string_type, input_value={'in': ['travel-agent', 'sports-agent']}, input_type=dict]\\n    For further information visit https://errors.pydantic.dev/2.10/v/string_type\"}", "timestamp": "2025-08-04T09:46:00.545528"}, {"test": "GTE/LTE Comparison Operators", "success": true, "details": "Found 2 results with priority 5-8", "timestamp": "2025-08-04T09:46:01.932074"}, {"test": "GT/LT Comparison Operators", "success": false, "details": "HTTP 500: {\"detail\":\"3 validation errors for MatchValue\\nvalue.bool\\n  Input should be a valid boolean [type=bool_type, input_value={'gt': 5, 'lt': 9}, input_type=dict]\\n    For further information visit https://errors.pydantic.dev/2.10/v/bool_type\\nvalue.int\\n  Input should be a valid integer [type=int_type, input_value={'gt': 5, 'lt': 9}, input_type=dict]\\n    For further information visit https://errors.pydantic.dev/2.10/v/int_type\\nvalue.str\\n  Input should be a valid string [type=string_type, input_value={'gt': 5, 'lt': 9}, input_type=dict]\\n    For further information visit https://errors.pydantic.dev/2.10/v/string_type\"}", "timestamp": "2025-08-04T09:46:03.329138"}, {"test": "NE (Not Equal) Operator", "success": false, "details": "HTTP 500: {\"detail\":\"3 validation errors for MatchValue\\nvalue.bool\\n  Input should be a valid boolean [type=bool_type, input_value={'ne': 'work'}, input_type=dict]\\n    For further information visit https://errors.pydantic.dev/2.10/v/bool_type\\nvalue.int\\n  Input should be a valid integer [type=int_type, input_value={'ne': 'work'}, input_type=dict]\\n    For further information visit https://errors.pydantic.dev/2.10/v/int_type\\nvalue.str\\n  Input should be a valid string [type=string_type, input_value={'ne': 'work'}, input_type=dict]\\n    For further information visit https://errors.pydantic.dev/2.10/v/string_type\"}", "timestamp": "2025-08-04T09:46:04.827923"}, {"test": "Wildcard Operator", "success": false, "details": "HTTP 400: {\"detail\":\"At least one identifier (user_id, agent_id, run_id) is required.\"}", "timestamp": "2025-08-04T09:46:04.831420"}, {"test": "ICONTAINS Operator", "success": true, "details": "Found 2 results with case-insensitive contains", "timestamp": "2025-08-04T09:46:06.109374"}, {"test": "Complex Nested Filters", "success": false, "details": "HTTP 400: {\"detail\":\"At least one identifier (user_id, agent_id, run_id) is required.\"}", "timestamp": "2025-08-04T09:46:06.112129"}, {"test": "Error <PERSON> - Missing Identifier", "success": true, "details": "Correctly returned 400 for missing identifier", "timestamp": "2025-08-04T09:46:06.114948"}]