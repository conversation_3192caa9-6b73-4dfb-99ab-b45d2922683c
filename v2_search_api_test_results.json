[{"test": "Basic V2 Search", "success": true, "details": "Found 2 results", "timestamp": "2025-08-04T09:37:33.964582"}, {"test": "OR Logical Operator", "success": false, "details": "HTTP 400: {\"detail\":\"At least one identifier (user_id, agent_id, run_id) is required.\"}", "timestamp": "2025-08-04T09:37:33.967426"}, {"test": "AND Logical Operator", "success": false, "details": "HTTP 400: {\"detail\":\"At least one identifier (user_id, agent_id, run_id) is required.\"}", "timestamp": "2025-08-04T09:37:33.970302"}, {"test": "IN Comparison Operator", "success": false, "details": "HTTP 500: {\"detail\":\"3 validation errors for MatchValue\\nvalue.bool\\n  Input should be a valid boolean [type=bool_type, input_value={'in': ['travel-agent', 'sports-agent']}, input_type=dict]\\n    For further information visit https://errors.pydantic.dev/2.10/v/bool_type\\nvalue.int\\n  Input should be a valid integer [type=int_type, input_value={'in': ['travel-agent', 'sports-agent']}, input_type=dict]\\n    For further information visit https://errors.pydantic.dev/2.10/v/int_type\\nvalue.str\\n  Input should be a valid string [type=string_type, input_value={'in': ['travel-agent', 'sports-agent']}, input_type=dict]\\n    For further information visit https://errors.pydantic.dev/2.10/v/string_type\"}", "timestamp": "2025-08-04T09:37:35.178527"}, {"test": "GTE/LTE Comparison Operators", "success": true, "details": "Found 2 results with priority 5-8", "timestamp": "2025-08-04T09:37:36.352342"}, {"test": "GT/LT Comparison Operators", "success": false, "details": "HTTP 500: {\"detail\":\"3 validation errors for MatchValue\\nvalue.bool\\n  Input should be a valid boolean [type=bool_type, input_value={'gt': 5, 'lt': 9}, input_type=dict]\\n    For further information visit https://errors.pydantic.dev/2.10/v/bool_type\\nvalue.int\\n  Input should be a valid integer [type=int_type, input_value={'gt': 5, 'lt': 9}, input_type=dict]\\n    For further information visit https://errors.pydantic.dev/2.10/v/int_type\\nvalue.str\\n  Input should be a valid string [type=string_type, input_value={'gt': 5, 'lt': 9}, input_type=dict]\\n    For further information visit https://errors.pydantic.dev/2.10/v/string_type\"}", "timestamp": "2025-08-04T09:37:37.469941"}, {"test": "NE (Not Equal) Operator", "success": false, "details": "HTTP 500: {\"detail\":\"3 validation errors for MatchValue\\nvalue.bool\\n  Input should be a valid boolean [type=bool_type, input_value={'ne': 'work'}, input_type=dict]\\n    For further information visit https://errors.pydantic.dev/2.10/v/bool_type\\nvalue.int\\n  Input should be a valid integer [type=int_type, input_value={'ne': 'work'}, input_type=dict]\\n    For further information visit https://errors.pydantic.dev/2.10/v/int_type\\nvalue.str\\n  Input should be a valid string [type=string_type, input_value={'ne': 'work'}, input_type=dict]\\n    For further information visit https://errors.pydantic.dev/2.10/v/string_type\"}", "timestamp": "2025-08-04T09:37:38.684092"}, {"test": "Wildcard Operator", "success": false, "details": "HTTP 400: {\"detail\":\"At least one identifier (user_id, agent_id, run_id) is required.\"}", "timestamp": "2025-08-04T09:37:38.687247"}, {"test": "ICONTAINS Operator", "success": true, "details": "Found 2 results with case-insensitive contains", "timestamp": "2025-08-04T09:37:39.897759"}, {"test": "Complex Nested Filters", "success": false, "details": "HTTP 400: {\"detail\":\"At least one identifier (user_id, agent_id, run_id) is required.\"}", "timestamp": "2025-08-04T09:37:39.900583"}, {"test": "Error <PERSON> - Missing Identifier", "success": true, "details": "Correctly returned 400 for missing identifier", "timestamp": "2025-08-04T09:37:39.903373"}]