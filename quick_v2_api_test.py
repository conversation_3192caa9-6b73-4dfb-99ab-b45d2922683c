#!/usr/bin/env python3
"""
Quick test script to verify v2 Search API functionality.
This script performs basic validation of the API endpoints and features.
"""

import requests
import json
import time

# Configuration
API_BASE_URL = "http://localhost:8000"

def test_api_health():
    """Test if the API server is running"""
    try:
        response = requests.get(f"{API_BASE_URL}/health/")
        if response.status_code == 200:
            print("✅ API server is running")
            return True
        else:
            print(f"❌ API health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to API server: {e}")
        return False

def test_v2_search_endpoint():
    """Test if v2 search endpoint exists"""
    try:
        # Test with minimal required data
        test_data = {
            "query": "test",
            "user_id": "test_user"
        }
        
        response = requests.post(f"{API_BASE_URL}/v2/memories/search/", json=test_data)
        
        if response.status_code in [200, 404]:  # 404 is OK if no memories exist
            print("✅ V2 search endpoint is accessible")
            return True
        elif response.status_code == 400:
            print("✅ V2 search endpoint exists (validation working)")
            return True
        else:
            print(f"❌ V2 search endpoint error: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ V2 search endpoint test failed: {e}")
        return False

def test_basic_memory_creation():
    """Test creating a basic memory"""
    try:
        memory_data = {
            "messages": [{"role": "user", "content": "Alice likes playing tennis."}],
            "user_id": "alice",
            "metadata": {"category": "hobbies", "priority": 5}
        }
        
        response = requests.post(f"{API_BASE_URL}/v1/memories/", json=memory_data)
        
        if response.status_code in [200, 201]:
            print("✅ Basic memory creation works")
            return True
        else:
            print(f"❌ Memory creation failed: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Memory creation test failed: {e}")
        return False

def test_v2_search_with_filters():
    """Test v2 search with basic filters"""
    try:
        # First create a test memory
        memory_data = {
            "messages": [{"role": "user", "content": "Alice enjoys reading books about science."}],
            "user_id": "alice",
            "agent_id": "reading-agent",
            "metadata": {"category": "hobbies", "priority": 7}
        }
        
        create_response = requests.post(f"{API_BASE_URL}/v1/memories/", json=memory_data)
        if create_response.status_code not in [200, 201]:
            print(f"⚠️ Could not create test memory: {create_response.status_code}")
            return False
        
        # Wait for indexing
        time.sleep(1)
        
        # Test basic search
        search_data = {
            "query": "What does Alice like?",
            "user_id": "alice"
        }
        
        response = requests.post(f"{API_BASE_URL}/v2/memories/search/", json=search_data)
        
        if response.status_code == 200:
            data = response.json()
            if "results" in data:
                print(f"✅ V2 search with basic filter works (found {len(data['results'])} results)")
                return True
            else:
                print("❌ V2 search response missing 'results' field")
                return False
        else:
            print(f"❌ V2 search failed: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ V2 search with filters test failed: {e}")
        return False

def test_v2_search_or_filter():
    """Test v2 search with OR filter (as shown in documentation)"""
    try:
        # Add user_id as required identifier
        search_data = {
            "query": "What are Alice's activities?",
            "user_id": "alice",  # Required identifier
            "filters": {
                "OR": [
                    {"category": "hobbies"},
                    {"agent_id": {"in": ["reading-agent", "sports-agent"]}}
                ]
            }
        }

        response = requests.post(f"{API_BASE_URL}/v2/memories/search/", json=search_data)

        if response.status_code == 200:
            data = response.json()
            if "results" in data:
                print(f"✅ V2 search with OR filter works (found {len(data['results'])} results)")
                return True
            else:
                print("❌ V2 search OR filter response missing 'results' field")
                return False
        else:
            print(f"❌ V2 search OR filter failed: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        print(f"❌ V2 search OR filter test failed: {e}")
        return False

def test_v2_search_comparison_operators():
    """Test v2 search with comparison operators"""
    try:
        search_data = {
            "query": "activities",
            "user_id": "alice",
            "filters": {
                "priority": {"gte": 5}
            }
        }
        
        response = requests.post(f"{API_BASE_URL}/v2/memories/search/", json=search_data)
        
        if response.status_code == 200:
            data = response.json()
            if "results" in data:
                print(f"✅ V2 search with comparison operators works (found {len(data['results'])} results)")
                return True
            else:
                print("❌ V2 search comparison operators response missing 'results' field")
                return False
        else:
            print(f"❌ V2 search comparison operators failed: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ V2 search comparison operators test failed: {e}")
        return False

def cleanup_test_data():
    """Clean up test data"""
    try:
        response = requests.delete(f"{API_BASE_URL}/v1/memories/", params={"user_id": "alice"})
        if response.status_code in [200, 404]:
            print("✅ Test data cleanup completed")
        else:
            print(f"⚠️ Cleanup warning: {response.status_code}")
    except Exception as e:
        print(f"⚠️ Cleanup failed: {e}")

def main():
    """Run quick API tests"""
    print("🚀 Quick V2 Search API Verification")
    print("=" * 40)
    
    tests = [
        ("API Health Check", test_api_health),
        ("V2 Search Endpoint", test_v2_search_endpoint),
        ("Basic Memory Creation", test_basic_memory_creation),
        ("V2 Search with Filters", test_v2_search_with_filters),
        ("V2 Search OR Filter", test_v2_search_or_filter),
        ("V2 Search Comparison Operators", test_v2_search_comparison_operators),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Cleanup
    print(f"\n🧹 Cleaning up...")
    cleanup_test_data()
    
    # Summary
    print(f"\n📊 Test Results Summary")
    print("=" * 40)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status}: {test_name}")
    
    print(f"\nTotal: {total}, Passed: {passed}, Failed: {total - passed}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 All tests passed! V2 Search API is working correctly.")
    else:
        print(f"\n⚠️ {total - passed} test(s) failed. Please check the API implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
