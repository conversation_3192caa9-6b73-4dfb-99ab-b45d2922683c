#!/usr/bin/env python3
"""
V2 Search API Demo - 展示所有高级功能的使用方法
包括：Advanced Retrieval, Custom Categories, Multimodal Support, 
Criteria Retrieval, Contextual Add v2, Selective Memory, 
Custom Instructions, Async Client, Graph Memory, Timestamp
"""

import os
import asyncio
from datetime import datetime, timedelta
from mem0 import MemoryClient, AsyncMemoryClient

def demo_basic_v2_search():
    """演示基础V2搜索功能"""
    print("=== 基础V2搜索演示 ===")
    
    client = MemoryClient()
    
    # 添加测试数据
    messages = [
        {"role": "user", "content": "我的名字是Alice，我喜欢打网球"},
        {"role": "assistant", "content": "你好Alice！网球是一项很棒的运动。"},
        {"role": "user", "content": "我也喜欢烹饪意大利菜"},
        {"role": "user", "content": "我对花生和贝类过敏"},
    ]
    
    try:
        client.add(messages, user_id="alice_demo")
        
        # 基础V2搜索
        results = client.search(
            query="Alice喜欢什么？",
            user_id="alice_demo",
            version="v2"
        )
        
        print(f"✅ 基础V2搜索找到 {len(results.get('memories', []))} 条记忆")
        for memory in results.get('memories', [])[:3]:
            print(f"  - {memory.get('memory', 'N/A')}")
            
    except Exception as e:
        print(f"❌ 基础V2搜索失败: {e}")
    
    return True

def demo_v2_search_with_filters():
    """演示V2搜索的复杂过滤器功能"""
    print("\n=== V2搜索过滤器演示 ===")
    
    client = MemoryClient()
    
    # 添加不同代理的测试数据
    messages1 = [
        {"role": "user", "content": "我喜欢打篮球"},
        {"role": "assistant", "content": "篮球很有趣！"}
    ]
    
    messages2 = [
        {"role": "user", "content": "我喜欢烹饪泰国菜"},
        {"role": "assistant", "content": "泰国菜很美味！"}
    ]
    
    try:
        client.add(messages1, user_id="filter_demo", agent_id="sports-agent")
        client.add(messages2, user_id="filter_demo", agent_id="cooking-agent")
        
        # OR过滤器测试
        results = client.search(
            query="我的爱好是什么？",
            version="v2",
            filters={
                "OR": [
                    {"user_id": "filter_demo"},
                    {"agent_id": {"in": ["sports-agent", "cooking-agent"]}}
                ]
            }
        )
        
        print(f"✅ OR过滤器搜索找到 {len(results.get('memories', []))} 条记忆")
        
        # AND过滤器测试
        results = client.search(
            query="我的爱好是什么？",
            version="v2",
            filters={
                "AND": [
                    {"user_id": "filter_demo"},
                    {"agent_id": "sports-agent"}
                ]
            }
        )
        
        print(f"✅ AND过滤器搜索找到 {len(results.get('memories', []))} 条记忆")
        
    except Exception as e:
        print(f"❌ 过滤器搜索失败: {e}")
    
    return True

def demo_advanced_retrieval():
    """演示高级检索功能"""
    print("\n=== 高级检索功能演示 ===")
    
    client = MemoryClient()
    
    # 添加测试数据
    messages = [
        {"role": "user", "content": "我喜欢打网球和篮球"},
        {"role": "assistant", "content": "太棒了！你最喜欢哪项运动？"},
        {"role": "user", "content": "我更喜欢网球，因为它更有策略性"},
        {"role": "user", "content": "我也喜欢在周末烹饪意大利菜"},
        {"role": "user", "content": "我最喜欢的网球选手是费德勒"},
    ]
    
    try:
        client.add(messages, user_id="advanced_demo")
        
        # 关键词搜索
        results = client.search(
            query="我喜欢什么运动？",
            user_id="advanced_demo",
            keyword_search=True
        )
        print(f"✅ 关键词搜索找到 {len(results.get('memories', []))} 条记忆")
        
        # 重新排序
        results = client.search(
            query="我喜欢什么运动？",
            user_id="advanced_demo",
            rerank=True
        )
        print(f"✅ 重新排序搜索找到 {len(results.get('memories', []))} 条记忆")
        
        # 记忆过滤
        results = client.search(
            query="我喜欢什么运动？",
            user_id="advanced_demo",
            filter_memories=True
        )
        print(f"✅ 记忆过滤搜索找到 {len(results.get('memories', []))} 条记忆")
        
    except Exception as e:
        print(f"❌ 高级检索失败: {e}")
    
    return True

def demo_criteria_retrieval():
    """演示标准检索功能"""
    print("\n=== 标准检索功能演示 ===")
    
    client = MemoryClient()
    
    # 定义检索标准
    retrieval_criteria = [
        {
            "name": "joy",
            "description": "测量句子中积极情绪的强度，如快乐、兴奋或愉悦。更高的分数反映更大的快乐。",
            "weight": 3
        },
        {
            "name": "curiosity",
            "description": "评估句子反映好奇心的程度，对探索新信息或提问的兴趣。更高的分数反映更强的好奇心。",
            "weight": 2
        }
    ]
    
    try:
        # 应用标准到项目
        client.project.update(retrieval_criteria=retrieval_criteria)
        
        # 添加测试记忆
        messages = [
            {"role": "user", "content": "多么美好的一天！我感觉如此清爽，准备迎接任何挑战！"},
            {"role": "user", "content": "我一直想知道风暴是如何形成的——是什么在大气中触发它们？"},
            {"role": "user", "content": "已经下了几天雨了，这让一切都感觉更沉重。"},
            {"role": "user", "content": "终于有时间画画了，好久没画了！！我今天超级开心。"}
        ]
        
        client.add(messages, user_id="criteria_demo")
        
        # 使用标准进行搜索
        results = client.search(
            query="为什么我今天感觉开心？",
            user_id="criteria_demo",
            version="v2"
        )
        
        print(f"✅ 标准检索搜索找到 {len(results.get('memories', []))} 条记忆")
        for memory in results.get('memories', [])[:3]:
            print(f"  - {memory.get('memory', 'N/A')} (分数: {memory.get('score', 'N/A'):.3f})")
            
    except Exception as e:
        print(f"❌ 标准检索失败: {e}")
    
    return True

def demo_graph_memory():
    """演示图记忆功能"""
    print("\n=== 图记忆功能演示 ===")
    
    client = MemoryClient()
    
    try:
        # 启用图记忆
        client.project.update(enable_graph=True)
        
        # 添加带图记忆的记忆
        messages = [
            {"role": "user", "content": "我的名字是Joseph"},
            {"role": "assistant", "content": "你好Joseph，很高兴认识你！"},
            {"role": "user", "content": "我来自西雅图，是一名软件工程师"}
        ]
        
        result = client.add(
            messages, 
            user_id="joseph_graph", 
            output_format="v1.1"
        )
        print(f"✅ 图记忆添加: {len(result.get('results', []))} 条记忆已添加")
        
        # 使用图记忆搜索
        search_result = client.search(
            "我的名字是什么？", 
            user_id="joseph_graph", 
            enable_graph=True, 
            output_format="v1.1"
        )
        
        print(f"✅ 图记忆搜索找到 {len(search_result.get('results', []))} 条记忆")
        if 'relations' in search_result:
            print(f"✅ 图关系: 找到 {len(search_result['relations'])} 个关系")
            
    except Exception as e:
        print(f"❌ 图记忆失败: {e}")
    
    return True

async def demo_async_client():
    """演示异步客户端功能"""
    print("\n=== 异步客户端功能演示 ===")
    
    client = AsyncMemoryClient()
    
    try:
        # 添加记忆
        messages = [
            {"role": "user", "content": "Alice喜欢打羽毛球"},
            {"role": "assistant", "content": "太棒了！Alice是个健身狂。"},
        ]
        
        result = await client.add(messages, user_id="alice_async")
        print(f"✅ 异步添加: {len(result.get('results', []))} 条记忆已添加")
        
        # 搜索记忆
        search_result = await client.search(
            "Alice最喜欢的运动是什么？", 
            user_id="alice_async"
        )
        print(f"✅ 异步搜索找到 {len(search_result.get('memories', []))} 条记忆")
        
    except Exception as e:
        print(f"❌ 异步客户端失败: {e}")
    
    return True

def demo_timestamp():
    """演示时间戳功能"""
    print("\n=== 时间戳功能演示 ===")
    
    client = MemoryClient()
    
    try:
        # 创建5天前的时间戳
        current_time = datetime.now()
        five_days_ago = current_time - timedelta(days=5)
        unix_timestamp = int(five_days_ago.timestamp())
        
        # 添加带自定义时间戳的记忆
        messages = [
            {"role": "user", "content": "我要去旧金山旅行"}
        ]
        
        result = client.add(messages, user_id="timestamp_demo", timestamp=unix_timestamp)
        print(f"✅ 时间戳记忆添加: {len(result.get('results', []))} 条记忆已添加")
        
        # 搜索验证
        search_result = client.search(
            "我有什么旅行计划？", 
            user_id="timestamp_demo"
        )
        print(f"✅ 时间戳搜索找到 {len(search_result.get('memories', []))} 条记忆")
        
    except Exception as e:
        print(f"❌ 时间戳功能失败: {e}")
    
    return True

def demo_custom_categories():
    """演示自定义分类功能"""
    print("\n=== 自定义分类功能演示 ===")
    
    client = MemoryClient()
    
    try:
        # 设置项目级自定义分类
        custom_categories = [
            {"sports_preferences": "用户喜欢的运动和体育活动"},
            {"food_preferences": "用户的饮食偏好和喜欢的菜系"},
            {"personal_info": "用户的基本个人信息"}
        ]
        
        response = client.project.update(custom_categories=custom_categories)
        print(f"✅ 项目更新响应: {response}")
        
        # 添加带自定义分类的记忆
        messages = [
            {"role": "user", "content": "我的名字是Sarah，我喜欢踢足球"},
            {"role": "assistant", "content": "你好Sarah！足球是一项很棒的运动。"},
            {"role": "user", "content": "我也喜欢吃寿司和泰国菜"},
            {"role": "user", "content": "我对花生和贝类过敏"},
        ]
        
        client.add(messages, user_id="sarah_categories")
        
        # 使用自定义分类搜索
        results = client.search(
            query="Sarah的饮食偏好是什么？",
            user_id="sarah_categories"
        )
        print(f"✅ 自定义分类搜索找到 {len(results.get('memories', []))} 条记忆")
        
    except Exception as e:
        print(f"❌ 自定义分类失败: {e}")
    
    return True

def demo_selective_memory():
    """演示选择性记忆功能"""
    print("\n=== 选择性记忆功能演示 ===")
    
    client = MemoryClient()
    
    try:
        # 测试记忆包含
        messages = [
            {"role": "user", "content": "我的名字是Alice，我喜欢打羽毛球"},
            {"role": "assistant", "content": "很高兴认识你，Alice！羽毛球是一项很棒的运动。"},
            {"role": "user", "content": "我喜欢音乐节"},
            {"role": "assistant", "content": "音乐节很刺激！你有最喜欢的吗？"},
            {"role": "user", "content": "我喜欢吃辣的食物"},
            {"role": "assistant", "content": "辣的食物很美味！你最喜欢的辣菜是什么？"},
            {"role": "user", "content": "我喜欢和朋友打棒球"},
            {"role": "assistant", "content": "和朋友打棒球听起来很有趣！"},
        ]
        
        # 测试包含过滤
        result_include = client.add(
            messages, 
            user_id="alice_selective", 
            includes="运动相关的事情"
        )
        print(f"✅ 包含过滤结果: {len(result_include.get('results', []))} 条记忆已添加")
        
        # 测试排除过滤
        result_exclude = client.add(
            messages, 
            user_id="alice_selective2", 
            excludes="饮食偏好"
        )
        print(f"✅ 排除过滤结果: {len(result_exclude.get('results', []))} 条记忆已添加")
        
    except Exception as e:
        print(f"❌ 选择性记忆失败: {e}")
    
    return True

def demo_custom_instructions():
    """演示自定义指令功能"""
    print("\n=== 自定义指令功能演示 ===")
    
    client = MemoryClient()
    
    try:
        # 设置自定义指令
        custom_instructions = """
        你的任务：仅从对话中提取健康相关信息，重点关注以下领域：

        1. 医疗条件、症状和诊断：
           - 疾病、障碍或症状（如发烧、糖尿病）。
           - 确认或疑似诊断。

        2. 药物、治疗和程序：
           - 处方或非处方药物（名称、剂量）。
           - 治疗、疗法或医疗程序。

        3. 饮食、运动和睡眠：
           - 饮食习惯、健身程序和睡眠模式。

        4. 医生访问和预约：
           - 过去、即将或定期的医疗访问。

        5. 健康指标：
           - 体重、血压、胆固醇或血糖水平等数据。

        指导原则：
        - 仅关注健康相关内容。
        - 在记录时保持清晰和上下文准确性。
        """
        
        response = client.project.update(custom_instructions=custom_instructions)
        print(f"✅ 自定义指令更新响应: {response}")
        
        # 添加健康相关消息
        messages = [
            {"role": "user", "content": "我有糖尿病，需要每天监测血糖"},
            {"role": "assistant", "content": "这很重要。你目前的血糖水平是多少？"},
            {"role": "user", "content": "我每天服用两次二甲双胍，并定期锻炼"},
            {"role": "user", "content": "我喜欢打网球和篮球"},
            {"role": "user", "content": "我的血压通常在120/80左右"},
        ]
        
        result = client.add(messages, user_id="health_user")
        print(f"✅ 健康相关记忆已添加: {len(result.get('results', []))} 条记忆")
        
    except Exception as e:
        print(f"❌ 自定义指令失败: {e}")
    
    return True

def run_all_demos():
    """运行所有演示"""
    print("开始V2搜索API功能演示")
    print("=" * 50)
    
    demos = [
        ("基础V2搜索", demo_basic_v2_search),
        ("V2搜索过滤器", demo_v2_search_with_filters),
        ("高级检索", demo_advanced_retrieval),
        ("标准检索", demo_criteria_retrieval),
        ("图记忆", demo_graph_memory),
        ("时间戳", demo_timestamp),
        ("自定义分类", demo_custom_categories),
        ("选择性记忆", demo_selective_memory),
        ("自定义指令", demo_custom_instructions),
    ]
    
    results = {}
    
    for demo_name, demo_func in demos:
        try:
            print(f"\n运行 {demo_name} 演示...")
            result = demo_func()
            results[demo_name] = "PASS" if result else "FAIL"
            print(f"{demo_name}: ✅ PASS")
        except Exception as e:
            print(f"{demo_name}: ❌ FAIL - {str(e)}")
            results[demo_name] = f"FAIL - {str(e)}"
    
    # 运行异步演示
    try:
        print("\n运行异步客户端演示...")
        result = asyncio.run(demo_async_client())
        results["异步客户端"] = "PASS" if result else "FAIL"
        print("异步客户端: ✅ PASS")
    except Exception as e:
        print(f"异步客户端: ❌ FAIL - {str(e)}")
        results["异步客户端"] = f"FAIL - {str(e)}"
    
    # 总结
    print("\n" + "=" * 50)
    print("演示总结")
    print("=" * 50)
    
    passed = sum(1 for result in results.values() if "PASS" in result)
    total = len(results)
    
    for demo_name, result in results.items():
        status = "✅ PASS" if "PASS" in result else "❌ FAIL"
        print(f"{demo_name}: {status}")
    
    print(f"\n总演示: {total}")
    print(f"成功: {passed}")
    print(f"失败: {total - passed}")
    print(f"成功率: {(passed/total*100):.1f}%")
    
    return results

if __name__ == "__main__":
    # 检查API密钥
    api_key = os.getenv("MEM0_API_KEY")
    if not api_key:
        print("警告: 未找到API密钥。请设置MEM0_API_KEY环境变量。")
        print("一些演示可能因认证问题而失败。")
    
    run_all_demos() 