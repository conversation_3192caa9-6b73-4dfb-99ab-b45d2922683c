#!/usr/bin/env python3
"""
Test script for v2 Search Memories API functionality verification.

This script tests the v2 search API features described in:
docs/api-reference/memory/v2-search-memories.mdx

Features tested:
- Complex logical operations (AND, OR, NOT)
- Comparison operators (in, gte, lte, gt, lt, ne, icontains, wildcard)
- Basic search functionality
- Error handling
"""

import json
import requests
import time
import sys
from typing import Dict, Any, List
from datetime import datetime

# Configuration
API_BASE_URL = "http://localhost:8000"
TEST_USER_ID = "alice"
TEST_AGENT_ID = "sports-agent"
TEST_RUN_ID = "test-run-123"

class V2SearchAPITester:
    def __init__(self, base_url: str = API_BASE_URL):
        self.base_url = base_url
        self.test_results = []
        self.setup_complete = False
        
    def log_test(self, test_name: str, success: bool, details: str = ""):
        """Log test results"""
        result = {
            "test": test_name,
            "success": success,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status}: {test_name}")
        if details:
            print(f"   Details: {details}")
    
    def setup_test_data(self):
        """Setup test memories for testing"""
        print("🔧 Setting up test data...")
        
        test_memories = [
            {
                "messages": [{"role": "user", "content": "Alice likes to play cricket and plays cricket on weekends."}],
                "user_id": TEST_USER_ID,
                "agent_id": TEST_AGENT_ID,
                "metadata": {"category": "hobbies", "priority": 5, "tags": ["sports", "weekend"]}
            },
            {
                "messages": [{"role": "user", "content": "Alice enjoys traveling to different countries."}],
                "user_id": TEST_USER_ID,
                "agent_id": "travel-agent",
                "metadata": {"category": "hobbies", "priority": 8, "tags": ["travel", "international"]}
            },
            {
                "messages": [{"role": "user", "content": "Bob likes playing football every Sunday."}],
                "user_id": "bob",
                "agent_id": TEST_AGENT_ID,
                "metadata": {"category": "hobbies", "priority": 6, "tags": ["sports", "weekend"]}
            },
            {
                "messages": [{"role": "user", "content": "Alice works as a software engineer."}],
                "user_id": TEST_USER_ID,
                "agent_id": "career-agent",
                "metadata": {"category": "work", "priority": 9, "tags": ["career", "technology"]}
            }
        ]
        
        try:
            for memory_data in test_memories:
                response = requests.post(f"{self.base_url}/v1/memories/", json=memory_data)
                if response.status_code not in [200, 201]:
                    print(f"⚠️ Warning: Failed to create test memory: {response.text}")
            
            # Wait a bit for indexing
            time.sleep(2)
            self.setup_complete = True
            print("✅ Test data setup complete")
            
        except Exception as e:
            print(f"❌ Failed to setup test data: {e}")
            self.setup_complete = False
    
    def test_basic_v2_search(self):
        """Test basic v2 search functionality"""
        test_data = {
            "query": "What are Alice's hobbies?",
            "user_id": TEST_USER_ID
        }
        
        try:
            response = requests.post(f"{self.base_url}/v2/memories/search/", json=test_data)
            
            if response.status_code == 200:
                data = response.json()
                has_results = "results" in data and len(data["results"]) > 0
                self.log_test("Basic V2 Search", has_results, 
                            f"Found {len(data.get('results', []))} results")
            else:
                self.log_test("Basic V2 Search", False, f"HTTP {response.status_code}: {response.text}")
                
        except Exception as e:
            self.log_test("Basic V2 Search", False, str(e))
    
    def test_or_logical_operator(self):
        """Test OR logical operator as shown in documentation"""
        test_data = {
            "query": "What are Alice's hobbies?",
            "filters": {
                "OR": [
                    {"user_id": "alice"},
                    {"agent_id": {"in": ["travel-agent", "sports-agent"]}}
                ]
            }
        }
        
        try:
            response = requests.post(f"{self.base_url}/v2/memories/search/", json=test_data)
            
            if response.status_code == 200:
                data = response.json()
                has_results = "results" in data and len(data["results"]) > 0
                self.log_test("OR Logical Operator", has_results,
                            f"Found {len(data.get('results', []))} results with OR filter")
            else:
                self.log_test("OR Logical Operator", False, f"HTTP {response.status_code}: {response.text}")
                
        except Exception as e:
            self.log_test("OR Logical Operator", False, str(e))
    
    def test_and_logical_operator(self):
        """Test AND logical operator"""
        test_data = {
            "query": "hobbies",
            "filters": {
                "AND": [
                    {"user_id": "alice"},
                    {"category": "hobbies"}
                ]
            }
        }
        
        try:
            response = requests.post(f"{self.base_url}/v2/memories/search/", json=test_data)
            
            if response.status_code == 200:
                data = response.json()
                has_results = "results" in data and len(data["results"]) > 0
                self.log_test("AND Logical Operator", has_results,
                            f"Found {len(data.get('results', []))} results with AND filter")
            else:
                self.log_test("AND Logical Operator", False, f"HTTP {response.status_code}: {response.text}")
                
        except Exception as e:
            self.log_test("AND Logical Operator", False, str(e))
    
    def test_in_comparison_operator(self):
        """Test 'in' comparison operator"""
        test_data = {
            "query": "sports activities",
            "user_id": TEST_USER_ID,
            "filters": {
                "agent_id": {"in": ["travel-agent", "sports-agent"]}
            }
        }
        
        try:
            response = requests.post(f"{self.base_url}/v2/memories/search/", json=test_data)
            
            if response.status_code == 200:
                data = response.json()
                has_results = "results" in data and len(data["results"]) > 0
                self.log_test("IN Comparison Operator", has_results,
                            f"Found {len(data.get('results', []))} results with 'in' filter")
            else:
                self.log_test("IN Comparison Operator", False, f"HTTP {response.status_code}: {response.text}")
                
        except Exception as e:
            self.log_test("IN Comparison Operator", False, str(e))
    
    def test_gte_lte_operators(self):
        """Test gte (greater than or equal) and lte (less than or equal) operators"""
        test_data = {
            "query": "activities",
            "user_id": TEST_USER_ID,
            "filters": {
                "priority": {"gte": 5, "lte": 8}
            }
        }
        
        try:
            response = requests.post(f"{self.base_url}/v2/memories/search/", json=test_data)
            
            if response.status_code == 200:
                data = response.json()
                has_results = "results" in data
                self.log_test("GTE/LTE Comparison Operators", has_results,
                            f"Found {len(data.get('results', []))} results with priority 5-8")
            else:
                self.log_test("GTE/LTE Comparison Operators", False, f"HTTP {response.status_code}: {response.text}")
                
        except Exception as e:
            self.log_test("GTE/LTE Comparison Operators", False, str(e))
    
    def test_wildcard_operator(self):
        """Test wildcard operator as shown in documentation"""
        test_data = {
            "query": "What are Alice's hobbies?",
            "filters": {
                "AND": [
                    {"user_id": "alice"},
                    {"run_id": "*"}
                ]
            }
        }
        
        try:
            response = requests.post(f"{self.base_url}/v2/memories/search/", json=test_data)
            
            if response.status_code == 200:
                data = response.json()
                has_results = "results" in data
                self.log_test("Wildcard Operator", has_results,
                            f"Found {len(data.get('results', []))} results with wildcard")
            else:
                self.log_test("Wildcard Operator", False, f"HTTP {response.status_code}: {response.text}")
                
        except Exception as e:
            self.log_test("Wildcard Operator", False, str(e))
    
    def test_icontains_operator(self):
        """Test icontains (case-insensitive contains) operator"""
        test_data = {
            "query": "activities",
            "user_id": TEST_USER_ID,
            "filters": {
                "category": {"icontains": "HOBB"}  # Should match "hobbies" case-insensitively
            }
        }

        try:
            response = requests.post(f"{self.base_url}/v2/memories/search/", json=test_data)

            if response.status_code == 200:
                data = response.json()
                has_results = "results" in data
                self.log_test("ICONTAINS Operator", has_results,
                            f"Found {len(data.get('results', []))} results with case-insensitive contains")
            else:
                self.log_test("ICONTAINS Operator", False, f"HTTP {response.status_code}: {response.text}")

        except Exception as e:
            self.log_test("ICONTAINS Operator", False, str(e))

    def test_gt_lt_operators(self):
        """Test gt (greater than) and lt (less than) operators"""
        test_data = {
            "query": "activities",
            "user_id": TEST_USER_ID,
            "filters": {
                "priority": {"gt": 5, "lt": 9}
            }
        }

        try:
            response = requests.post(f"{self.base_url}/v2/memories/search/", json=test_data)

            if response.status_code == 200:
                data = response.json()
                has_results = "results" in data
                self.log_test("GT/LT Comparison Operators", has_results,
                            f"Found {len(data.get('results', []))} results with priority > 5 and < 9")
            else:
                self.log_test("GT/LT Comparison Operators", False, f"HTTP {response.status_code}: {response.text}")

        except Exception as e:
            self.log_test("GT/LT Comparison Operators", False, str(e))

    def test_ne_operator(self):
        """Test ne (not equal) operator"""
        test_data = {
            "query": "activities",
            "user_id": TEST_USER_ID,
            "filters": {
                "category": {"ne": "work"}
            }
        }

        try:
            response = requests.post(f"{self.base_url}/v2/memories/search/", json=test_data)

            if response.status_code == 200:
                data = response.json()
                has_results = "results" in data
                self.log_test("NE (Not Equal) Operator", has_results,
                            f"Found {len(data.get('results', []))} results where category != 'work'")
            else:
                self.log_test("NE (Not Equal) Operator", False, f"HTTP {response.status_code}: {response.text}")

        except Exception as e:
            self.log_test("NE (Not Equal) Operator", False, str(e))

    def test_complex_nested_filters(self):
        """Test complex nested filters combining multiple operators"""
        test_data = {
            "query": "What does Alice do?",
            "filters": {
                "AND": [
                    {"user_id": "alice"},
                    {
                        "OR": [
                            {"category": "hobbies"},
                            {"priority": {"gte": 8}}
                        ]
                    }
                ]
            }
        }

        try:
            response = requests.post(f"{self.base_url}/v2/memories/search/", json=test_data)

            if response.status_code == 200:
                data = response.json()
                has_results = "results" in data
                self.log_test("Complex Nested Filters", has_results,
                            f"Found {len(data.get('results', []))} results with complex nested filters")
            else:
                self.log_test("Complex Nested Filters", False, f"HTTP {response.status_code}: {response.text}")

        except Exception as e:
            self.log_test("Complex Nested Filters", False, str(e))
    
    def test_error_handling(self):
        """Test error handling for invalid requests"""
        # Test missing required identifier
        test_data = {
            "query": "test query",
            "filters": {"category": "hobbies"}
        }
        
        try:
            response = requests.post(f"{self.base_url}/v2/memories/search/", json=test_data)
            
            # Should return 400 error for missing identifier
            if response.status_code == 400:
                self.log_test("Error Handling - Missing Identifier", True,
                            "Correctly returned 400 for missing identifier")
            else:
                self.log_test("Error Handling - Missing Identifier", False,
                            f"Expected 400, got {response.status_code}")
                
        except Exception as e:
            self.log_test("Error Handling - Missing Identifier", False, str(e))
    
    def cleanup_test_data(self):
        """Clean up test data"""
        print("🧹 Cleaning up test data...")
        try:
            # Delete all memories for test users
            for user_id in [TEST_USER_ID, "bob"]:
                response = requests.delete(f"{self.base_url}/v1/memories/", params={"user_id": user_id})
                if response.status_code not in [200, 404]:
                    print(f"⚠️ Warning: Failed to cleanup memories for {user_id}")
            print("✅ Cleanup complete")
        except Exception as e:
            print(f"⚠️ Cleanup failed: {e}")
    
    def run_all_tests(self):
        """Run all tests"""
        print("🚀 Starting V2 Search API Tests")
        print("=" * 50)
        
        # Setup
        self.setup_test_data()
        if not self.setup_complete:
            print("❌ Cannot proceed without test data setup")
            return False
        
        # Run tests
        self.test_basic_v2_search()
        self.test_or_logical_operator()
        self.test_and_logical_operator()
        self.test_in_comparison_operator()
        self.test_gte_lte_operators()
        self.test_gt_lt_operators()
        self.test_ne_operator()
        self.test_wildcard_operator()
        self.test_icontains_operator()
        self.test_complex_nested_filters()
        self.test_error_handling()
        
        # Cleanup
        self.cleanup_test_data()
        
        # Summary
        print("\n" + "=" * 50)
        print("📊 Test Summary")
        print("=" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ Failed Tests:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test']}: {result['details']}")
        
        return failed_tests == 0

def main():
    """Main function"""
    tester = V2SearchAPITester()
    success = tester.run_all_tests()
    
    # Save detailed results
    with open("v2_search_api_test_results.json", "w") as f:
        json.dump(tester.test_results, f, indent=2)
    
    print(f"\n📄 Detailed results saved to: v2_search_api_test_results.json")
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
